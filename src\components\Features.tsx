import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Code2, Zap, Shield, Globe, Layers, Wallet } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: Code2,
      title: "No-Code Development",
      description: "Build and deploy smart contracts without writing a single line of code. Our ghost writer handles the complexity.",
      badge: "Developer Friendly"
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Built on Arbitrum Orbit for blazing fast transactions and minimal fees. Your users will love the speed.",
      badge: "High Performance"
    },
    {
      icon: Shield,
      title: "Battle-Tested Security",
      description: "Inherit Ethereum's security while enjoying the flexibility of a custom rollup. Best of both worlds.",
      badge: "Secure by Design"
    },
    {
      icon: Globe,
      title: "Full Web3 Stack",
      description: "From frontend to smart contracts, we provide everything you need to build modern decentralized applications.",
      badge: "Complete Solution"
    },
    {
      icon: Layers,
      title: "Modular Architecture",
      description: "Pick and choose the components you need. Scale up or down based on your project requirements.",
      badge: "Flexible"
    },
    {
      icon: Wallet,
      title: "Wallet Integration",
      description: "Seamless integration with all major wallets. Your users can connect with just one click.",
      badge: "User Friendly"
    }
  ];

  return (
    <section className="py-24 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4 bg-primary/10 text-primary border-primary/20">
            <Code2 className="w-3 h-3 mr-1" />
            Platform Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
            Why Choose Ghost Developer?
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            We handle the blockchain complexity so you can focus on building amazing user experiences.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index} 
                className="bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/50 transition-all duration-300 hover:shadow-glow-primary/20 group"
              >
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center group-hover:shadow-glow-primary transition-all duration-300">
                      <Icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    <Badge variant="secondary" className="bg-accent/10 text-accent border-accent/20 text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl font-bold">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 text-sm text-muted-foreground bg-secondary/50 px-4 py-2 rounded-full">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
            Live on Arbitrum Orbit • Ready for Production
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;