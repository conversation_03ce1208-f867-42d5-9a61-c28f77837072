import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ghost, Plus, LayoutDashboard, Wallet } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useChainId } from 'wagmi';
import { boredomcore } from '@/config/wagmi';

const Header: React.FC = () => {
  const location = useLocation();
  const { isConnected } = useAccount();
  const chainId = useChainId();
  const isOnBoredomcore = chainId === boredomcore.id;

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link to="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <Ghost className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="text-lg font-bold bg-gradient-primary bg-clip-text text-transparent">
            Ghost Developer
          </span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-4">
          <Link to="/">
            <Button
              variant={isActive("/") ? "secondary" : "ghost"}
              className="flex items-center gap-2"
            >
              <Ghost className="w-4 h-4" />
              Home
            </Button>
          </Link>

          <Link to="/dashboard">
            <Button
              variant={isActive("/dashboard") ? "secondary" : "ghost"}
              className="flex items-center gap-2"
            >
              <LayoutDashboard className="w-4 h-4" />
              Dashboard
            </Button>
          </Link>

          <Link to="/create/token">
            <Button
              variant={isActive("/create/token") ? "secondary" : "ghost"}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create Token
            </Button>
          </Link>

          <Link to="/create/nft">
            <Button
              variant={isActive("/create/nft") ? "secondary" : "ghost"}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create NFT
            </Button>
          </Link>
        </nav>

        {/* Connect Wallet Button */}
        <div className="flex items-center space-x-2">
          <Badge
            variant="secondary"
            className={`${isConnected && isOnBoredomcore
              ? 'bg-green-500/10 text-green-500 border-green-500/20'
              : 'bg-primary/10 text-primary border-primary/20'
              }`}
          >
            <div className={`w-2 h-2 rounded-full animate-pulse mr-1 ${isConnected && isOnBoredomcore ? 'bg-green-500' : 'bg-primary'
              }`} />
            {isConnected && isOnBoredomcore ? 'Boredomcore' : 'Live'}
          </Badge>
          <ConnectButton />
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden border-t border-border/40 bg-background/95 backdrop-blur">
        <div className="container mx-auto flex items-center justify-around py-2 px-4">
          <Link to="/">
            <Button
              variant={isActive("/") ? "secondary" : "ghost"}
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <Ghost className="w-4 h-4" />
              <span className="text-xs">Home</span>
            </Button>
          </Link>

          <Link to="/dashboard">
            <Button
              variant={isActive("/dashboard") ? "secondary" : "ghost"}
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <LayoutDashboard className="w-4 h-4" />
              <span className="text-xs">Dashboard</span>
            </Button>
          </Link>

          <Link to="/create/token">
            <Button
              variant={isActive("/create/token") ? "secondary" : "ghost"}
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <Plus className="w-4 h-4" />
              <span className="text-xs">Token</span>
            </Button>
          </Link>

          <Link to="/create/nft">
            <Button
              variant={isActive("/create/nft") ? "secondary" : "ghost"}
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <Plus className="w-4 h-4" />
              <span className="text-xs">NFT</span>
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
};

export default Header;