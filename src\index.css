@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 20% 7%;
    --foreground: 200 20% 95%;

    --card: 220 15% 10%;
    --card-foreground: 200 20% 95%;

    --popover: 220 15% 10%;
    --popover-foreground: 200 20% 95%;

    --primary: 195 100% 50%;
    --primary-foreground: 220 20% 7%;

    --secondary: 240 15% 15%;
    --secondary-foreground: 200 20% 90%;

    --muted: 240 10% 20%;
    --muted-foreground: 200 10% 60%;

    --accent: 280 80% 60%;
    --accent-foreground: 220 20% 7%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 10% 20%;
    --input: 240 10% 20%;
    --ring: 195 100% 50%;

    --radius: 1rem;

    /* Ghost-themed gradients */
    --gradient-primary: linear-gradient(135deg, hsl(195 100% 50%), hsl(220 100% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(280 80% 60%), hsl(195 100% 50%));
    --gradient-ghost: linear-gradient(180deg, hsl(195 100% 85%), hsl(220 100% 95%));
    --gradient-background: radial-gradient(ellipse at top, hsl(220 30% 15%), hsl(220 20% 7%));
    
    /* Glowing effects */
    --glow-primary: 0 0 30px hsl(195 100% 50% / 0.3);
    --glow-accent: 0 0 30px hsl(280 80% 60% / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}