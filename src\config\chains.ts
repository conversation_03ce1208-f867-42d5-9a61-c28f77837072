import { define<PERSON>hain } from 'viem'

// Your Alchemy Rollup Chain Configuration
export const boredomcore = defineChain({
  id: 1205614519731336,
  name: 'boredomcore',
  nativeCurrency: {
    decimals: 18,
    name: 'Ether',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: [import.meta.env.VITE_RPC_URL || 'https://rpc.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5'],
      webSocket: [import.meta.env.VITE_WS_URL || 'https://ws.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Boredomcore Explorer',
      url: import.meta.env.VITE_EXPLORER_URL || 'https://explorer-1205614519731336.devnet.alchemy.com',
    },
  },
  contracts: {
    // Add your rollup contracts here if needed
  },
  testnet: true, // Set to false if this is mainnet
})

// Ethereum Sepolia (Parent Chain)
export const sepolia = defineChain({
  id: 11155111,
  name: 'Sepolia',
  nativeCurrency: {
    decimals: 18,
    name: 'Sepolia Ether',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: [import.meta.env.VITE_PARENT_RPC_URL || 'https://eth-sepolia.g.alchemy.com/v2/demo'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://sepolia.etherscan.io',
    },
  },
  testnet: true,
})

export const supportedChains = [boredomcore, sepolia] as const
