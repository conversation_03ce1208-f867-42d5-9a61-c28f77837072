const hre = require("hardhat");

async function main() {
  const [deployer] = await hre.ethers.getSigners();

  console.log("Deploying GhostNFT with the account:", deployer.address);
  console.log("Account balance:", (await deployer.provider.getBalance(deployer.address)).toString());

  // Get deployment parameters from environment or use defaults
  const name = process.env.NFT_NAME || "Ghost NFT";
  const symbol = process.env.NFT_SYMBOL || "GNFT";
  const description = process.env.NFT_DESCRIPTION || "A collection of ghost NFTs";
  const externalURL = process.env.NFT_EXTERNAL_URL || "";
  const baseTokenURI = process.env.NFT_BASE_URI || "";
  const owner = process.env.NFT_OWNER || deployer.address;

  const GhostNFT = await hre.ethers.getContractFactory("GhostNFT");
  const nft = await GhostNFT.deploy(name, symbol, description, externalURL, baseTokenURI, owner);

  await nft.waitForDeployment();

  const nftAddress = await nft.getAddress();
  console.log("GhostNFT deployed to:", nftAddress);
  console.log("NFT Name:", name);
  console.log("NFT Symbol:", symbol);
  console.log("Description:", description);
  console.log("External URL:", externalURL);
  console.log("Base URI:", baseTokenURI);
  console.log("Owner:", owner);

  // Verify contract on explorer if not on hardhat network
  if (hre.network.name !== "hardhat") {
    console.log("Waiting for block confirmations...");
    await nft.deploymentTransaction().wait(5);
    
    try {
      await hre.run("verify:verify", {
        address: nftAddress,
        constructorArguments: [name, symbol, description, externalURL, baseTokenURI, owner],
      });
    } catch (error) {
      console.log("Verification failed:", error.message);
    }
  }

  return {
    address: nftAddress,
    name,
    symbol,
    description,
    externalURL,
    baseTokenURI,
    owner
  };
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
if (require.main === module) {
  main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
  });
}

module.exports = main;
