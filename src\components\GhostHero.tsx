import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ghost, Code, Zap, Network } from "lucide-react";

const GhostHero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-background" />
      
      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-primary/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-4 text-center">
        {/* Badge */}
        <Badge variant="secondary" className="mb-6 bg-primary/10 text-primary border-primary/20">
          <Zap className="w-3 h-3 mr-1" />
          Powered by Alchemy Rollups
        </Badge>

        {/* Ghost Logo */}
        <div className="mb-8 flex justify-center">
          <div className="relative">
            <div className="w-32 h-32 bg-gradient-ghost rounded-full flex items-center justify-center shadow-glow-primary">
              <img 
                src="/lovable-uploads/1b70575f-e1b2-4252-b944-09a2cc70d574.png" 
                alt="Ghost Developer Logo" 
                className="w-24 h-24"
              />
            </div>
            <div className="absolute -inset-4 bg-gradient-primary rounded-full blur-xl opacity-20 animate-pulse" />
          </div>
        </div>

        {/* Main Heading */}
        <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
          Ghost Developer
        </h1>
        
        <p className="text-xl md:text-2xl text-muted-foreground mb-4 max-w-2xl mx-auto">
          Your invisible partner in blockchain development
        </p>
        
        <p className="text-lg text-muted-foreground/80 mb-12 max-w-xl mx-auto">
          Like a ghost writer, but for code. Build, deploy, and manage your dApps on our custom Arbitrum rollup.
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
          <Button variant="gradient" size="xl" className="group">
            <Code className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
            Start Building
          </Button>
          <Button variant="ghostly" size="xl" className="group">
            <Network className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
            Explore Network
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          {[
            { label: "Chain ID", value: "1205614519731336" },
            { label: "Framework", value: "Arbitrum Orbit" },
            { label: "Gas Token", value: "ETH" },
            { label: "Status", value: "Live ✨" },
          ].map((stat, index) => (
            <div key={index} className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-lg p-4 hover:border-primary/50 transition-colors">
              <div className="text-2xl font-bold text-primary">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default GhostHero;