import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Copy, ExternalLink, Globe, Zap, Shield, Layers } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAccount, useChainId } from 'wagmi';
import { boredomcore } from '@/config/wagmi';

const NetworkInfo = () => {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const isOnBoredomcore = chainId === boredomcore.id;

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `${label} has been copied to your clipboard.`,
    });
  };

  const networkData = {
    chainInfo: {
      chainId: "****************",
      chainName: "boredomcore",
      parentChainId: "******** (Sepolia)",
      rpcUrl: "https://ru_g81fP_e1wCVRiejvPRXwQ.g.alchemy.com/public",
      explorerUrl: "https://explorer-****************.devnet.alchemy.com",
      nativeToken: "ETH via Sepolia",
      framework: "Arbitrum Orbit",
      settlementLayer: "Ethereum Sepolia",
      dataAvailability: "Ethereum DA",
      dateCreated: "7/16/2025"
    },
    endpoints: {
      rpc: "https://rpc.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5",
      ws: "https://ws.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5",
      explorer: "https://explorer-****************.devnet.alchemy.com"
    }
  };

  return (
    <section className="py-24 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4 bg-accent/10 text-accent border-accent/20">
            <Globe className="w-3 h-3 mr-1" />
            Network Information
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-secondary bg-clip-text text-transparent">
            boredomcore Network
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Your custom Arbitrum rollup is live and ready for development. Here's everything you need to connect.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Quick Connect */}
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/50 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-primary" />
                Quick Connect
              </CardTitle>
              <CardDescription>
                Essential connection details for your wallet and dApps
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-secondary/50 rounded-lg">
                  <div>
                    <div className="font-medium">Chain ID</div>
                    <div className="text-sm text-muted-foreground font-mono">{networkData.chainInfo.chainId}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(networkData.chainInfo.chainId, "Chain ID")}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex justify-between items-center p-3 bg-secondary/50 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">RPC URL</div>
                    <div className="text-sm text-muted-foreground font-mono truncate">{networkData.chainInfo.rpcUrl}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(networkData.chainInfo.rpcUrl, "RPC URL")}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex justify-between items-center p-3 bg-secondary/50 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">Explorer</div>
                    <div className="text-sm text-muted-foreground font-mono truncate">{networkData.endpoints.explorer}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(networkData.endpoints.explorer, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Network Details */}
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:border-accent/50 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="w-5 h-5 text-accent" />
                Network Details
              </CardTitle>
              <CardDescription>
                Technical specifications and architecture
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Framework</div>
                  <div className="font-medium">{networkData.chainInfo.framework}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Settlement</div>
                  <div className="font-medium">{networkData.chainInfo.settlementLayer}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Data Availability</div>
                  <div className="font-medium">{networkData.chainInfo.dataAvailability}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Gas Token</div>
                  <div className="font-medium">{networkData.chainInfo.nativeToken}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Parent Chain</div>
                  <div className="font-medium">{networkData.chainInfo.parentChainId}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Created</div>
                  <div className="font-medium">{networkData.chainInfo.dateCreated}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Developer Tools */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-primary" />
              Developer Tools & Endpoints
            </CardTitle>
            <CardDescription>
              Additional endpoints and tools for advanced development
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">WebSocket Endpoint</h4>
                <div className="flex items-center justify-between p-3 bg-secondary/50 rounded-lg">
                  <code className="text-sm font-mono text-muted-foreground truncate flex-1 mr-2">
                    {networkData.endpoints.ws}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(networkData.endpoints.ws, "WebSocket URL")}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Your Wallet</h4>
                <div className="flex items-center justify-between p-3 bg-secondary/50 rounded-lg">
                  {isConnected && address ? (
                    <>
                      <code className="text-sm font-mono text-muted-foreground truncate flex-1 mr-2">
                        {address}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(address, "Wallet Address")}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      Connect your wallet to see your address
                    </div>
                  )}
                </div>
                {isConnected && !isOnBoredomcore && (
                  <div className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">
                      ⚠️ You're not connected to the boredomcore network. Switch networks to interact with contracts.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NetworkInfo;