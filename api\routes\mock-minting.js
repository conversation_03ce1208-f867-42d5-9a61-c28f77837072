const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Generate a mock transaction hash
function generateMockTxHash() {
  return '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('');
}

// Mock Mint ERC20 tokens
router.post('/mint-token', async (req, res) => {
  try {
    const { contractAddress, toAddress, amount, chain } = req.body;
    
    if (!contractAddress || !toAddress || !amount) {
      return res.status(400).json({ 
        error: 'Missing required fields: contractAddress, toAddress, amount' 
      });
    }

    // Simulate minting delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const txHash = generateMockTxHash();
    
    console.log(`Mock minting ${amount} tokens to ${toAddress} on contract ${contractAddress}`);
    
    res.json({
      success: true,
      txHash,
      contractAddress,
      toAddress,
      amount: amount.toString(),
      amountInWei: (BigInt(amount) * BigInt(10**18)).toString()
    });
    
  } catch (error) {
    console.error('Mock token minting error:', error);
    res.status(500).json({ 
      error: 'Failed to mint tokens',
      message: error.message 
    });
  }
});

// Mock Mint ERC721 NFT
router.post('/mint-nft', async (req, res) => {
  try {
    const { contractAddress, toAddress, metadata, chain } = req.body;
    
    if (!contractAddress || !toAddress || !metadata) {
      return res.status(400).json({ 
        error: 'Missing required fields: contractAddress, toAddress, metadata' 
      });
    }

    // Simulate minting delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const txHash = generateMockTxHash();
    const tokenId = Math.floor(Math.random() * 10000);
    
    // Create metadata URI
    const metadataString = JSON.stringify(metadata);
    const tokenURI = `data:application/json;base64,${Buffer.from(metadataString).toString('base64')}`;
    
    console.log(`Mock minting NFT to ${toAddress} on contract ${contractAddress}`);
    console.log(`Metadata:`, metadata);
    
    res.json({
      success: true,
      txHash,
      contractAddress,
      toAddress,
      tokenId: tokenId.toString(),
      metadata,
      tokenURI
    });
    
  } catch (error) {
    console.error('Mock NFT minting error:', error);
    res.status(500).json({ 
      error: 'Failed to mint NFT',
      message: error.message 
    });
  }
});

// Mock Get token balance
router.get('/balance/:contractAddress/:address', async (req, res) => {
  try {
    const { contractAddress, address } = req.params;
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate mock balance
    const balance = Math.floor(Math.random() * 1000000);
    const decimals = 18;
    const symbol = 'GHOST';
    
    res.json({
      balance: (BigInt(balance) * BigInt(10**decimals)).toString(),
      balanceFormatted: balance.toString(),
      decimals: decimals.toString(),
      symbol
    });
    
  } catch (error) {
    console.error('Mock balance check error:', error);
    res.status(500).json({ 
      error: 'Failed to get balance',
      message: error.message 
    });
  }
});

module.exports = router;
