const express = require('express');
const multer = require('multer');
const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'public/uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 25 * 1024 * 1024 } // 25MB
});

// Load contract ABIs and bytecode
function loadContract(contractName) {
  try {
    const artifactPath = path.join(__dirname, '../../artifacts/contracts', `${contractName}.sol`, `${contractName}.json`);
    const artifact = JSON.parse(fs.readFileSync(artifactPath, 'utf8'));
    return {
      abi: artifact.abi,
      bytecode: artifact.bytecode
    };
  } catch (error) {
    console.error(`Failed to load contract ${contractName}:`, error);
    throw new Error(`Contract ${contractName} not found. Please compile contracts first.`);
  }
}

// Initialize provider and wallet
function getProvider() {
  const rpcUrl = process.env.VITE_RPC_URL;
  if (!rpcUrl) {
    throw new Error('RPC URL not configured');
  }
  return new ethers.JsonRpcProvider(rpcUrl);
}

function getWallet() {
  const privateKey = process.env.PRIVATE_KEY;
  if (!privateKey) {
    throw new Error('Private key not configured');
  }
  const provider = getProvider();
  return new ethers.Wallet(privateKey, provider);
}

// Deploy Token Contract
router.post('/create-token-contract', upload.single('image'), async (req, res) => {
  try {
    const { name, symbol, initialSupply, decimals, chain } = req.body;

    if (!name || !symbol || !chain) {
      return res.status(400).json({ error: 'Missing required fields: name, symbol, chain' });
    }

    // Load contract
    const { abi, bytecode } = loadContract('GhostToken');

    // Get wallet and deploy
    const wallet = getWallet();
    const contractFactory = new ethers.ContractFactory(abi, bytecode, wallet);

    const deploymentArgs = [
      name,
      symbol,
      parseInt(decimals) || 18,
      initialSupply || 0,
      wallet.address
    ];

    console.log('Deploying token with args:', deploymentArgs);

    const contract = await contractFactory.deploy(...deploymentArgs);
    await contract.waitForDeployment();

    const contractAddress = await contract.getAddress();

    // Save contract info to database/file (simplified for now)
    const contractInfo = {
      contractAddress,
      name,
      symbol,
      decimals: parseInt(decimals) || 18,
      initialSupply: initialSupply || 0,
      chain,
      imageURI: req.file ? `/uploads/${req.file.filename}` : null,
      deployedAt: new Date().toISOString(),
      type: 'ERC20'
    };

    // Save to a simple JSON file (in production, use a proper database)
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    let contracts = [];

    if (fs.existsSync(contractsFile)) {
      contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    }

    contracts.push(contractInfo);

    // Ensure data directory exists
    const dataDir = path.dirname(contractsFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    fs.writeFileSync(contractsFile, JSON.stringify(contracts, null, 2));

    res.json({
      success: true,
      contractAddress,
      transactionHash: contract.deploymentTransaction().hash,
      ...contractInfo
    });

  } catch (error) {
    console.error('Token deployment error:', error);
    res.status(500).json({
      error: 'Failed to deploy token contract',
      message: error.message
    });
  }
});

// Deploy NFT Contract
router.post('/create-nft-contract', upload.single('image'), async (req, res) => {
  try {
    const { name, symbol, description, externalURL, chain } = req.body;

    if (!name || !symbol || !chain) {
      return res.status(400).json({ error: 'Missing required fields: name, symbol, chain' });
    }

    // Load contract
    const { abi, bytecode } = loadContract('GhostNFT');

    // Get wallet and deploy
    const wallet = getWallet();
    const contractFactory = new ethers.ContractFactory(abi, bytecode, wallet);

    const deploymentArgs = [
      name,
      symbol,
      description || '',
      externalURL || '',
      '', // baseTokenURI - can be set later
      wallet.address
    ];

    console.log('Deploying NFT with args:', deploymentArgs);

    const contract = await contractFactory.deploy(...deploymentArgs);
    await contract.waitForDeployment();

    const contractAddress = await contract.getAddress();

    // Save contract info
    const contractInfo = {
      contractAddress,
      name,
      symbol,
      description: description || '',
      externalURL: externalURL || '',
      chain,
      imageURI: req.file ? `/uploads/${req.file.filename}` : null,
      deployedAt: new Date().toISOString(),
      type: 'ERC721'
    };

    // Save to contracts file
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    let contracts = [];

    if (fs.existsSync(contractsFile)) {
      contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    }

    contracts.push(contractInfo);

    // Ensure data directory exists
    const dataDir = path.dirname(contractsFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    fs.writeFileSync(contractsFile, JSON.stringify(contracts, null, 2));

    res.json({
      success: true,
      contractAddress,
      transactionHash: contract.deploymentTransaction().hash,
      ...contractInfo
    });

  } catch (error) {
    console.error('NFT deployment error:', error);
    res.status(500).json({
      error: 'Failed to deploy NFT contract',
      message: error.message
    });
  }
});

module.exports = router;
