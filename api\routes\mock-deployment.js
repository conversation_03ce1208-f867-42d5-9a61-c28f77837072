const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'public/uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 25 * 1024 * 1024 } // 25MB
});

// Generate a mock contract address
function generateMockAddress() {
  return '0x' + Array.from({length: 40}, () => Math.floor(Math.random() * 16).toString(16)).join('');
}

// Generate a mock transaction hash
function generateMockTxHash() {
  return '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('');
}

// Mock Deploy Token Contract
router.post('/create-token-contract', upload.single('image'), async (req, res) => {
  try {
    const { name, symbol, initialSupply, decimals, chain } = req.body;
    
    if (!name || !symbol || !chain) {
      return res.status(400).json({ error: 'Missing required fields: name, symbol, chain' });
    }

    // Simulate deployment delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const contractAddress = generateMockAddress();
    const txHash = generateMockTxHash();
    
    // Save contract info to mock database
    const contractInfo = {
      contractAddress,
      name,
      symbol,
      decimals: parseInt(decimals) || 18,
      initialSupply: initialSupply || 0,
      chain,
      imageURI: req.file ? `/uploads/${req.file.filename}` : null,
      deployedAt: new Date().toISOString(),
      type: 'ERC20',
      status: 'deployed'
    };
    
    // Save to a simple JSON file
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    let contracts = [];
    
    if (fs.existsSync(contractsFile)) {
      contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    }
    
    contracts.push(contractInfo);
    
    // Ensure data directory exists
    const dataDir = path.dirname(contractsFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(contractsFile, JSON.stringify(contracts, null, 2));
    
    res.json({
      success: true,
      contractAddress,
      transactionHash: txHash,
      ...contractInfo
    });
    
  } catch (error) {
    console.error('Mock token deployment error:', error);
    res.status(500).json({ 
      error: 'Failed to deploy token contract',
      message: error.message 
    });
  }
});

// Mock Deploy NFT Contract
router.post('/create-nft-contract', upload.single('image'), async (req, res) => {
  try {
    const { name, symbol, description, externalURL, chain } = req.body;
    
    if (!name || !symbol || !chain) {
      return res.status(400).json({ error: 'Missing required fields: name, symbol, chain' });
    }

    // Simulate deployment delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const contractAddress = generateMockAddress();
    const txHash = generateMockTxHash();
    
    // Save contract info
    const contractInfo = {
      contractAddress,
      name,
      symbol,
      description: description || '',
      externalURL: externalURL || '',
      chain,
      imageURI: req.file ? `/uploads/${req.file.filename}` : null,
      deployedAt: new Date().toISOString(),
      type: 'ERC721',
      status: 'deployed'
    };
    
    // Save to contracts file
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    let contracts = [];
    
    if (fs.existsSync(contractsFile)) {
      contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    }
    
    contracts.push(contractInfo);
    
    // Ensure data directory exists
    const dataDir = path.dirname(contractsFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(contractsFile, JSON.stringify(contracts, null, 2));
    
    res.json({
      success: true,
      contractAddress,
      transactionHash: txHash,
      ...contractInfo
    });
    
  } catch (error) {
    console.error('Mock NFT deployment error:', error);
    res.status(500).json({ 
      error: 'Failed to deploy NFT contract',
      message: error.message 
    });
  }
});

module.exports = router;
