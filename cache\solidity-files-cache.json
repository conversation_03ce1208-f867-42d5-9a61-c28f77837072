{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\contracts\\GhostNFT.sol": {"lastModificationDate": 1754435739339, "contentHash": "b50f25eb0177611dcea914ece1b06046", "sourceName": "contracts/GhostNFT.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC721/ERC721.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["GhostNFT"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1754427221439, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\ERC721.sol": {"lastModificationDate": 1754427220633, "contentHash": "522ec023bce510c30dfbf61584f5a190", "sourceName": "@openzeppelin/contracts/token/ERC721/ERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC721.sol", "./extensions/IERC721Metadata.sol", "./utils/ERC721Utils.sol", "../../utils/Context.sol", "../../utils/Strings.sol", "../../utils/introspection/ERC165.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721URIStorage.sol": {"lastModificationDate": 1754427220667, "contentHash": "0b00dd0d9f2738855f17aa283bfa3ff5", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "./IERC721Metadata.sol", "../../../utils/Strings.sol", "../../../interfaces/IERC4906.sol", "../../../interfaces/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721URIStorage"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721Burnable.sol": {"lastModificationDate": 1754427220646, "contentHash": "594379619f21d2767c325a6c46b53399", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Burnable"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1754427220213, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\Strings.sol": {"lastModificationDate": 1754427221610, "contentHash": "d8f70caf0e0c77dc908176ed44812fb7", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1754427220411, "contentHash": "5041977bbe908de2e6ed0270447f79ad", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721.sol": {"lastModificationDate": 1754427221228, "contentHash": "f62e11dbd302e17a5621a1438f4e054c", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC721"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol": {"lastModificationDate": 1754427220513, "contentHash": "0906d06dca25210d4696dcef6dad2909", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\IERC721Metadata.sol": {"lastModificationDate": 1754427221257, "contentHash": "136c64eca25bcb8a68f43ac6605559f9", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC721Metadata"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\utils\\ERC721Utils.sol": {"lastModificationDate": 1754427220674, "contentHash": "4cefad279a37c895b709bce2b901816e", "sourceName": "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721Receiver.sol", "../../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Utils"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SignedMath.sol": {"lastModificationDate": 1754427221591, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SafeCast.sol": {"lastModificationDate": 1754427221569, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\math\\Math.sol": {"lastModificationDate": 1754427221396, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\Panic.sol": {"lastModificationDate": 1754427221501, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol": {"lastModificationDate": 1754427221095, "contentHash": "7074c93b1ea0a122063f26ddd1db1032", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC165"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721Receiver.sol": {"lastModificationDate": 1754427221321, "contentHash": "d61660a41ce200e99816e4734f7fd202", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IERC721Receiver"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC165.sol": {"lastModificationDate": 1754427221090, "contentHash": "947853028399b7de34bcc3704ee06e99", "sourceName": "@openzeppelin/contracts/interfaces/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": [">=0.4.16"], "artifacts": []}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC4906.sol": {"lastModificationDate": 1754427221161, "contentHash": "4386d2e9b1578157802062e156bd9ae1", "sourceName": "@openzeppelin/contracts/interfaces/IERC4906.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol", "./IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC4906"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC721.sol": {"lastModificationDate": 1754427221219, "contentHash": "6b9716905fb42874ed1769ae7f7458b1", "sourceName": "@openzeppelin/contracts/interfaces/IERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../token/ERC721/IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": []}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Burnable.sol": {"lastModificationDate": 1754427220543, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1754427220536, "contentHash": "59dfce11284f2636db261df9b6a18f81", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1754427221111, "contentHash": "9261adf6457863de3e9892f51317ec89", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1754427221126, "contentHash": "513778b30d2750f5d2b9b19bbcf748a5", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC20Metadata"]}, "C:\\Users\\<USER>\\ghost dev studio\\ghost-developer-studio\\contracts\\GhostToken.sol": {"lastModificationDate": 1754435753817, "contentHash": "29f431c002566cd59579b00b9b344646", "sourceName": "contracts/GhostToken.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["GhostToken"]}}}