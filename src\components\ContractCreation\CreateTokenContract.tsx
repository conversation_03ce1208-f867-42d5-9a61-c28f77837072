import React, { useState, use<PERSON><PERSON>back } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Upload, Coins, AlertCircle, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
const ALLOWED_TYPES = [
  "image/jpeg",
  "image/jpg", 
  "image/png",
  "image/gif",
  "image/bmp",
  "image/webp",
  "image/svg+xml",
];

const CHAINS = [
  { label: "Ethereum Mainnet", value: "ethereum" },
  { label: "Ethereum Goerli", value: "ethereumGoerli" },
  { label: "Polygon", value: "polygon" },
  { label: "Polygon Mumbai", value: "polygonMumbai" },
  { label: "Binance Smart Chain", value: "bsc" },
  { label: "Avalanche", value: "avalanche" },
];

const CreateTokenContract: React.FC = () => {
  const [formData, setFormData] = useState({
    name: "",
    symbol: "",
    initialSupply: "",
    decimals: "18",
    chain: "",
  });
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);

  const onDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setError(null);
    if (e.dataTransfer.files.length === 0) return;

    const file = e.dataTransfer.files[0];
    validateAndSetFile(file);
  }, []);

  const validateAndSetFile = (file: File) => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      setError("Unsupported file type. Please upload: jpeg, jpg, png, gif, bmp, webp, svg");
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      setError("File too large. Maximum size is 25MB");
      return;
    }

    setImageFile(file);
    setError(null);
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      validateAndSetFile(e.target.files[0]);
    }
  };

  const onDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const canSubmit = formData.name.trim().length > 0 && 
                   formData.symbol.trim().length > 0 && 
                   formData.chain.length > 0 && 
                   imageFile !== null;

  const onSubmit = async () => {
    if (!canSubmit) return;

    setUploading(true);
    setError(null);

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("name", formData.name.trim());
      formDataToSend.append("symbol", formData.symbol.trim());
      formDataToSend.append("initialSupply", formData.initialSupply || "0");
      formDataToSend.append("decimals", formData.decimals);
      formDataToSend.append("chain", formData.chain);
      if (imageFile) formDataToSend.append("image", imageFile);

      const response = await fetch("/api/create-token-contract", {
        method: "POST",
        body: formDataToSend,
      });

      if (!response.ok) {
        throw new Error("Failed to create contract");
      }

      const data = await response.json();

      toast({
        title: "Token Contract Created! 🎉",
        description: `Contract deployed at: ${data.contractAddress.slice(0, 10)}...`,
      });

      // Reset form
      setFormData({
        name: "",
        symbol: "",
        initialSupply: "",
        decimals: "18",
        chain: "",
      });
      setImageFile(null);
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Creation Failed",
        description: "Failed to create token contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="text-center">
        <Badge variant="secondary" className="mb-4 bg-primary/10 text-primary border-primary/20">
          <Coins className="w-3 h-3 mr-1" />
          Token Creation
        </Badge>
        <h1 className="text-3xl font-bold mb-2 bg-gradient-primary bg-clip-text text-transparent">
          Create Token Contract
        </h1>
        <p className="text-muted-foreground">
          Deploy your custom ERC20 token on any supported blockchain
        </p>
      </div>

      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Coins className="w-4 h-4 text-primary-foreground" />
            </div>
            Token Metadata
          </CardTitle>
          <CardDescription>
            Configure your token's basic properties and upload a logo
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Token Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Ghost Token"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="bg-secondary/50"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="symbol">Token Symbol *</Label>
              <Input
                id="symbol"
                placeholder="e.g., GHOST"
                value={formData.symbol}
                onChange={(e) => setFormData({ ...formData, symbol: e.target.value.toUpperCase() })}
                className="bg-secondary/50"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="chain">Blockchain *</Label>
            <Select value={formData.chain} onValueChange={(value) => setFormData({ ...formData, chain: value })}>
              <SelectTrigger className="bg-secondary/50">
                <SelectValue placeholder="Select blockchain network" />
              </SelectTrigger>
              <SelectContent>
                {CHAINS.map((chain) => (
                  <SelectItem key={chain.value} value={chain.value}>
                    {chain.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {showMoreOptions && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-secondary/30 rounded-lg">
              <div className="space-y-2">
                <Label htmlFor="initialSupply">Initial Supply</Label>
                <Input
                  id="initialSupply"
                  placeholder="0 (can mint later)"
                  value={formData.initialSupply}
                  onChange={(e) => setFormData({ ...formData, initialSupply: e.target.value })}
                  className="bg-background/50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="decimals">Decimals</Label>
                <Input
                  id="decimals"
                  placeholder="18"
                  value={formData.decimals}
                  onChange={(e) => setFormData({ ...formData, decimals: e.target.value })}
                  className="bg-background/50"
                />
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label>Token Logo *</Label>
            <div
              onDrop={onDrop}
              onDragOver={onDragOver}
              className="border-2 border-dashed border-primary/20 rounded-lg p-8 text-center cursor-pointer hover:border-primary/40 transition-colors bg-secondary/20"
              onClick={() => document.getElementById("image-upload")?.click()}
            >
              {imageFile ? (
                <div className="space-y-2">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
                    <Upload className="w-8 h-8 text-primary" />
                  </div>
                  <p className="font-medium">{imageFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(imageFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                  <p className="text-xs text-muted-foreground">Click to replace</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
                    <Upload className="w-8 h-8 text-primary" />
                  </div>
                  <p className="font-medium">Drop your logo here or click to upload</p>
                  <p className="text-sm text-muted-foreground">
                    Supported: JPEG, PNG, GIF, BMP, WebP, SVG
                  </p>
                  <p className="text-xs text-muted-foreground">Maximum size: 25MB</p>
                </div>
              )}
            </div>
            <input
              type="file"
              id="image-upload"
              accept={ALLOWED_TYPES.join(",")}
              onChange={onFileChange}
              className="hidden"
            />
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-lg">
              <AlertCircle className="w-4 h-4" />
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            <Button
              variant="ghost"
              onClick={() => setShowMoreOptions(!showMoreOptions)}
              className="w-full"
            >
              {showMoreOptions ? "Hide" : "Show"} advanced options
            </Button>

            <Button
              onClick={onSubmit}
              disabled={!canSubmit || uploading}
              className="w-full"
              variant="gradient"
              size="lg"
            >
              {uploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating Contract...
                </>
              ) : (
                <>
                  <Coins className="w-4 h-4 mr-2" />
                  Create Token Contract
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreateTokenContract;