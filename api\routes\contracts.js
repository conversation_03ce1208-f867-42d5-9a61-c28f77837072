const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Get all contracts or filter by type
router.get('/', (req, res) => {
  try {
    const { type } = req.query;
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    
    if (!fs.existsSync(contractsFile)) {
      return res.json([]);
    }
    
    let contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    
    // Filter by type if specified
    if (type) {
      const filterType = type.toLowerCase();
      contracts = contracts.filter(contract => {
        if (filterType === 'erc20') return contract.type === 'ERC20';
        if (filterType === 'erc721') return contract.type === 'ERC721';
        return contract.type === type;
      });
    }
    
    res.json(contracts);
  } catch (error) {
    console.error('Error fetching contracts:', error);
    res.status(500).json({ error: 'Failed to fetch contracts' });
  }
});

// Get specific contract by address
router.get('/:address', (req, res) => {
  try {
    const { address } = req.params;
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    
    if (!fs.existsSync(contractsFile)) {
      return res.status(404).json({ error: 'Contract not found' });
    }
    
    const contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    const contract = contracts.find(c => c.contractAddress.toLowerCase() === address.toLowerCase());
    
    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }
    
    res.json(contract);
  } catch (error) {
    console.error('Error fetching contract:', error);
    res.status(500).json({ error: 'Failed to fetch contract' });
  }
});

// Delete contract (remove from list)
router.delete('/:address', (req, res) => {
  try {
    const { address } = req.params;
    const contractsFile = path.join(__dirname, '../data/contracts.json');
    
    if (!fs.existsSync(contractsFile)) {
      return res.status(404).json({ error: 'Contract not found' });
    }
    
    let contracts = JSON.parse(fs.readFileSync(contractsFile, 'utf8'));
    const initialLength = contracts.length;
    
    contracts = contracts.filter(c => c.contractAddress.toLowerCase() !== address.toLowerCase());
    
    if (contracts.length === initialLength) {
      return res.status(404).json({ error: 'Contract not found' });
    }
    
    fs.writeFileSync(contractsFile, JSON.stringify(contracts, null, 2));
    
    res.json({ success: true, message: 'Contract removed from list' });
  } catch (error) {
    console.error('Error deleting contract:', error);
    res.status(500).json({ error: 'Failed to delete contract' });
  }
});

module.exports = router;
