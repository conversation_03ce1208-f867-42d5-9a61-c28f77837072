// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ofkjbstekuwsrcpmkwqb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ma2pic3Rla3V3c3JjcG1rd3FiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4NDY5NjcsImV4cCI6MjA2OTQyMjk2N30.G1KRSLwVcVdo7hg-JePkOTBZFAkNag7tGPqprvUD8HM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});