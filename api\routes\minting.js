const express = require('express');
const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Load contract ABI
function loadContractABI(contractName) {
  try {
    const artifactPath = path.join(__dirname, '../../artifacts/contracts', `${contractName}.sol`, `${contractName}.json`);
    const artifact = JSON.parse(fs.readFileSync(artifactPath, 'utf8'));
    return artifact.abi;
  } catch (error) {
    console.error(`Failed to load contract ABI ${contractName}:`, error);
    throw new Error(`Contract ${contractName} not found. Please compile contracts first.`);
  }
}

// Initialize provider and wallet
function getProvider() {
  const rpcUrl = process.env.VITE_RPC_URL;
  if (!rpcUrl) {
    throw new Error('RPC URL not configured');
  }
  return new ethers.JsonRpcProvider(rpcUrl);
}

function getWallet() {
  const privateKey = process.env.PRIVATE_KEY;
  if (!privateKey) {
    throw new Error('Private key not configured');
  }
  const provider = getProvider();
  return new ethers.Wallet(privateKey, provider);
}

// Mint ERC20 tokens
router.post('/mint-token', async (req, res) => {
  try {
    const { contractAddress, toAddress, amount, chain } = req.body;
    
    if (!contractAddress || !toAddress || !amount) {
      return res.status(400).json({ 
        error: 'Missing required fields: contractAddress, toAddress, amount' 
      });
    }

    // Load contract ABI
    const abi = loadContractABI('GhostToken');
    
    // Get wallet and connect to contract
    const wallet = getWallet();
    const contract = new ethers.Contract(contractAddress, abi, wallet);
    
    // Convert amount to proper units (assuming 18 decimals)
    const decimals = await contract.decimals();
    const amountInWei = ethers.parseUnits(amount.toString(), decimals);
    
    console.log(`Minting ${amount} tokens to ${toAddress}`);
    console.log(`Amount in wei: ${amountInWei.toString()}`);
    
    // Call mint function
    const tx = await contract.mint(toAddress, amountInWei);
    await tx.wait();
    
    res.json({
      success: true,
      txHash: tx.hash,
      contractAddress,
      toAddress,
      amount: amount.toString(),
      amountInWei: amountInWei.toString()
    });
    
  } catch (error) {
    console.error('Token minting error:', error);
    res.status(500).json({ 
      error: 'Failed to mint tokens',
      message: error.message 
    });
  }
});

// Mint ERC721 NFT
router.post('/mint-nft', async (req, res) => {
  try {
    const { contractAddress, toAddress, metadata, chain } = req.body;
    
    if (!contractAddress || !toAddress || !metadata) {
      return res.status(400).json({ 
        error: 'Missing required fields: contractAddress, toAddress, metadata' 
      });
    }

    // Create metadata JSON and upload to IPFS or store locally
    // For now, we'll create a simple metadata URI
    const metadataString = JSON.stringify(metadata);
    const metadataHash = ethers.keccak256(ethers.toUtf8Bytes(metadataString));
    const tokenURI = `data:application/json;base64,${Buffer.from(metadataString).toString('base64')}`;
    
    // Load contract ABI
    const abi = loadContractABI('GhostNFT');
    
    // Get wallet and connect to contract
    const wallet = getWallet();
    const contract = new ethers.Contract(contractAddress, abi, wallet);
    
    console.log(`Minting NFT to ${toAddress}`);
    console.log(`Metadata:`, metadata);
    
    // Call mint function
    const tx = await contract.mint(toAddress, tokenURI);
    const receipt = await tx.wait();
    
    // Extract token ID from events
    let tokenId = null;
    for (const log of receipt.logs) {
      try {
        const parsedLog = contract.interface.parseLog(log);
        if (parsedLog.name === 'Transfer' && parsedLog.args.from === ethers.ZeroAddress) {
          tokenId = parsedLog.args.tokenId.toString();
          break;
        }
      } catch (e) {
        // Ignore parsing errors for logs from other contracts
      }
    }
    
    res.json({
      success: true,
      txHash: tx.hash,
      contractAddress,
      toAddress,
      tokenId: tokenId || 'Unknown',
      metadata,
      tokenURI
    });
    
  } catch (error) {
    console.error('NFT minting error:', error);
    res.status(500).json({ 
      error: 'Failed to mint NFT',
      message: error.message 
    });
  }
});

// Get token balance
router.get('/balance/:contractAddress/:address', async (req, res) => {
  try {
    const { contractAddress, address } = req.params;
    
    // Load contract ABI
    const abi = loadContractABI('GhostToken');
    
    // Get provider and connect to contract
    const provider = getProvider();
    const contract = new ethers.Contract(contractAddress, abi, provider);
    
    const balance = await contract.balanceOf(address);
    const decimals = await contract.decimals();
    const symbol = await contract.symbol();
    
    res.json({
      balance: balance.toString(),
      balanceFormatted: ethers.formatUnits(balance, decimals),
      decimals: decimals.toString(),
      symbol
    });
    
  } catch (error) {
    console.error('Balance check error:', error);
    res.status(500).json({ 
      error: 'Failed to get balance',
      message: error.message 
    });
  }
});

module.exports = router;
