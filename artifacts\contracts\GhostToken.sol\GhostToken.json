{"_format": "hh-sol-artifact-1", "contractName": "GhostToken", "sourceName": "contracts/GhostToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchMint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}