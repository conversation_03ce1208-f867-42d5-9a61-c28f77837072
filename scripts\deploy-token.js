const hre = require("hardhat");

async function main() {
  const [deployer] = await hre.ethers.getSigners();

  console.log("Deploying GhostToken with the account:", deployer.address);
  console.log("Account balance:", (await deployer.provider.getBalance(deployer.address)).toString());

  // Get deployment parameters from environment or use defaults
  const name = process.env.TOKEN_NAME || "Ghost Token";
  const symbol = process.env.TOKEN_SYMBOL || "GHOST";
  const decimals = process.env.TOKEN_DECIMALS || 18;
  const initialSupply = process.env.TOKEN_INITIAL_SUPPLY || 0;
  const owner = process.env.TOKEN_OWNER || deployer.address;

  const GhostToken = await hre.ethers.getContractFactory("GhostToken");
  const token = await GhostToken.deploy(name, symbol, decimals, initialSupply, owner);

  await token.waitForDeployment();

  const tokenAddress = await token.getAddress();
  console.log("GhostToken deployed to:", tokenAddress);
  console.log("Token Name:", name);
  console.log("Token Symbol:", symbol);
  console.log("Token Decimals:", decimals);
  console.log("Initial Supply:", initialSupply);
  console.log("Owner:", owner);

  // Verify contract on explorer if not on hardhat network
  if (hre.network.name !== "hardhat") {
    console.log("Waiting for block confirmations...");
    await token.deploymentTransaction().wait(5);
    
    try {
      await hre.run("verify:verify", {
        address: tokenAddress,
        constructorArguments: [name, symbol, decimals, initialSupply, owner],
      });
    } catch (error) {
      console.log("Verification failed:", error.message);
    }
  }

  return {
    address: tokenAddress,
    name,
    symbol,
    decimals,
    initialSupply,
    owner
  };
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
if (require.main === module) {
  main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
  });
}

module.exports = main;
