# Ghost Developer Studio

A no-code DApp for creating and managing ERC20 tokens and ERC721 NFTs on your Alchemy rollup "boredomcore".

## 🚀 Features

- **Wallet Connection**: Connect with MetaMask and other Web3 wallets
- **Token Creation**: Deploy custom ERC20 tokens with configurable parameters
- **NFT Creation**: Deploy ERC721 NFT collections with metadata
- **Token Minting**: Mint tokens and NFTs to any address
- **Dashboard**: View and manage all your deployed contracts
- **Network Support**: Built specifically for your boredomcore Alchemy rollup

## 🛠️ Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- MetaMask or compatible Web3 wallet

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

The `.env.local` file is already configured with your boredomcore network settings.

### 3. Get a WalletConnect Project ID

1. Go to [WalletConnect Cloud](https://cloud.walletconnect.com)
2. Create a new project
3. Copy your Project ID
4. Update `src/config/wagmi.ts` with your real Project ID

### 4. Start the Development Servers

Start the frontend:

```bash
npm run dev
```

In a new terminal, start the API server:

```bash
npm run api
```

### 5. Add boredomcore Network to MetaMask

Add the following network to your MetaMask:

- **Network Name**: boredomcore
- **RPC URL**: `https://rpc.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5`
- **Chain ID**: `1205614519731336`
- **Currency Symbol**: `ETH`
- **Block Explorer**: `https://explorer-1205614519731336.devnet.alchemy.com`

## 🎯 Usage

### Creating Tokens

1. Navigate to "Create Token" page
2. Fill in token details (name, symbol, etc.)
3. Upload a token logo
4. Select the boredomcore network
5. Click "Create Token Contract"

### Creating NFTs

1. Navigate to "Create NFT" page
2. Fill in collection details
3. Upload collection artwork
4. Deploy your NFT contract

### Managing Contracts

1. Go to the Dashboard
2. View all your deployed contracts
3. Mint tokens or NFTs directly from the interface

## 🌐 Network Information

- **Chain ID**: 1205614519731336
- **Network Name**: boredomcore
- **Framework**: Arbitrum Orbit
- **Settlement Layer**: Ethereum Sepolia
- **RPC URL**: <https://rpc.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5>
- **Explorer**: <https://explorer-1205614519731336.devnet.alchemy.com>

## 🚨 Current Status

✅ Frontend UI complete
✅ Wallet connection working
✅ Mock API endpoints functional
✅ Network configuration set up
⚠️ Smart contract compilation needs fixing
⚠️ Real contract deployment pending

## 🔄 Next Steps

1. Fix Hardhat configuration for contract compilation
2. Add your private key for real deployments
3. Test contract deployment on boredomcore testnet
4. Add more advanced features (batch operations, etc.)

## 🆘 Troubleshooting

- **Wallet not connecting**: Make sure you have the boredomcore network added to MetaMask
- **API errors**: Ensure the API server is running on port 3001
- **Contract deployment fails**: Check that you have sufficient ETH and correct private key

## 📞 Support

If you need help setting this up or want to add more features, feel free to ask!
