// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
/**
 * @title GhostNFT
 * @dev ERC721 NFT template for Ghost Developer Studio
 * Features: URI Storage, Burnable, Ownable, Auto-incrementing token IDs
 */
contract GhostNFT is ERC721, ERC721URIStorage, ERC721Burnable, Ownable {
    uint256 private _tokenIdCounter;
    string private _baseTokenURI;
    
    // Collection metadata
    string public description;
    string public externalURL;
    
    constructor(
        string memory name,
        string memory symbol,
        string memory description_,
        string memory externalURL_,
        string memory baseTokenURI,
        address owner
    ) ERC721(name, symbol) Ownable(owner) {
        description = description_;
        externalURL = externalURL_;
        _baseTokenURI = baseTokenURI;
    }

    /**
     * @dev Mint a new NFT to the specified address
     */
    function mint(address to, string memory uri) public onlyOwner returns (uint256) {
        uint256 tokenId = _tokenIdCounter;
        _tokenIdCounter++;

        _safeMint(to, tokenId);
        _setTokenURI(tokenId, uri);

        return tokenId;
    }

    /**
     * @dev Batch mint NFTs to multiple addresses
     */
    function batchMint(
        address[] memory recipients,
        string[] memory uris
    ) public onlyOwner returns (uint256[] memory) {
        require(recipients.length == uris.length, "Arrays length mismatch");

        uint256[] memory tokenIds = new uint256[](recipients.length);

        for (uint256 i = 0; i < recipients.length; i++) {
            tokenIds[i] = mint(recipients[i], uris[i]);
        }

        return tokenIds;
    }

    /**
     * @dev Set the base URI for token metadata
     */
    function setBaseURI(string memory baseTokenURI) public onlyOwner {
        _baseTokenURI = baseTokenURI;
    }

    /**
     * @dev Update collection metadata
     */
    function setCollectionMetadata(
        string memory description_,
        string memory externalURL_
    ) public onlyOwner {
        description = description_;
        externalURL = externalURL_;
    }

    /**
     * @dev Get the current token ID counter
     */
    function getCurrentTokenId() public view returns (uint256) {
        return _tokenIdCounter;
    }

    // Override required functions
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }

    function tokenURI(uint256 tokenId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    function _update(address to, uint256 tokenId, address auth)
        internal
        override(ERC721)
        returns (address)
    {
        return super._update(to, tokenId, auth);
    }
}
