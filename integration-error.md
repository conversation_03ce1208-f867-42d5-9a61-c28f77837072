# CS-Script Integration Error

## Issue Description

This document describes integration issues with CS-Script and provides solutions.

## Prerequisites

Note: you need to have .NET SDK installed for using CS-Script (see <https://dotnet.microsoft.com/en-us/download>)

## Common Issues

- Configuration problems
- Missing dependencies
- Runtime errors

## Solutions

1. Verify .NET SDK installation
2. Check CS-Script configuration
3. Review error logs
