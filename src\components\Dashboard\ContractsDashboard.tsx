import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Coins, Image, Search, ExternalLink, Hammer, Copy, Plus, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface TokenContract {
  contractAddress: string;
  name: string;
  symbol: string;
  decimals: number;
  initialSupply: string;
  chain: string;
  imageURI: string | null;
  deployedAt: string;
}

interface NFTContract {
  contractAddress: string;
  name: string;
  symbol: string;
  description: string;
  externalURL: string;
  chain: string;
  imageURI: string | null;
  deployedAt: string;
}

const ContractsDashboard: React.FC = () => {
  const [tokenContracts, setTokenContracts] = useState<TokenContract[]>([]);
  const [nftContracts, setNFTContracts] = useState<NFTContract[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [mintLoading, setMintLoading] = useState<string | null>(null);

  // Mint form states
  const [mintTokenData, setMintTokenData] = useState({
    contractAddress: "",
    toAddress: "",
    amount: "",
    chain: "",
  });

  const [mintNFTData, setMintNFTData] = useState({
    contractAddress: "",
    toAddress: "",
    name: "",
    description: "",
    image: "",
    chain: "",
  });

  useEffect(() => {
    fetchContracts();
  }, []);

  const fetchContracts = async () => {
    try {
      const [tokenResponse, nftResponse] = await Promise.all([
        fetch("/api/contracts?type=erc20"),
        fetch("/api/contracts?type=erc721"),
      ]);

      if (tokenResponse.ok) {
        const tokens = await tokenResponse.json();
        setTokenContracts(tokens);
      }

      if (nftResponse.ok) {
        const nfts = await nftResponse.json();
        setNFTContracts(nfts);
      }
    } catch (error) {
      console.error("Failed to fetch contracts:", error);
      toast({
        title: "Failed to load contracts",
        description: "Please try refreshing the page",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${label} copied to clipboard`,
    });
  };

  const getExplorerUrl = (chain: string, address: string) => {
    // This should be expanded to include all supported chains
    const explorers: Record<string, string> = {
      ethereum: "https://etherscan.io/address/",
      ethereumGoerli: "https://goerli.etherscan.io/address/",
      polygon: "https://polygonscan.com/address/",
      polygonMumbai: "https://mumbai.polygonscan.com/address/",
      bsc: "https://bscscan.com/address/",
      avalanche: "https://snowtrace.io/address/",
    };
    return `${explorers[chain] || "#"}${address}`;
  };

  const mintToken = async () => {
    if (!mintTokenData.contractAddress || !mintTokenData.toAddress || !mintTokenData.amount) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setMintLoading(mintTokenData.contractAddress);

    try {
      const response = await fetch("/api/mint-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(mintTokenData),
      });

      if (!response.ok) {
        throw new Error("Failed to mint tokens");
      }

      const data = await response.json();

      toast({
        title: "Tokens Minted! 🎉",
        description: `Transaction hash: ${data.txHash.slice(0, 10)}...`,
      });

      setMintTokenData({
        contractAddress: "",
        toAddress: "",
        amount: "",
        chain: "",
      });
    } catch (error) {
      toast({
        title: "Minting Failed",
        description: "Failed to mint tokens. Please try again.",
        variant: "destructive",
      });
    } finally {
      setMintLoading(null);
    }
  };

  const mintNFT = async () => {
    if (!mintNFTData.contractAddress || !mintNFTData.toAddress || !mintNFTData.name) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setMintLoading(mintNFTData.contractAddress);

    try {
      const metadata = {
        name: mintNFTData.name,
        description: mintNFTData.description,
        image: mintNFTData.image,
      };

      const response = await fetch("/api/mint-nft", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractAddress: mintNFTData.contractAddress,
          toAddress: mintNFTData.toAddress,
          metadata,
          chain: mintNFTData.chain,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to mint NFT");
      }

      const data = await response.json();

      toast({
        title: "NFT Minted! 🎉",
        description: `Token ID: ${data.tokenId}`,
      });

      setMintNFTData({
        contractAddress: "",
        toAddress: "",
        name: "",
        description: "",
        image: "",
        chain: "",
      });
    } catch (error) {
      toast({
        title: "Minting Failed",
        description: "Failed to mint NFT. Please try again.",
        variant: "destructive",
      });
    } finally {
      setMintLoading(null);
    }
  };

  const filteredTokens = tokenContracts.filter(
    (contract) =>
      contract.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.contractAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredNFTs = nftContracts.filter(
    (contract) =>
      contract.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.contractAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2 bg-gradient-primary bg-clip-text text-transparent">
          Contracts Dashboard
        </h1>
        <p className="text-muted-foreground">
          Manage your deployed token and NFT contracts
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search contracts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-secondary/50"
          />
        </div>
      </div>

      <Tabs defaultValue="tokens" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="tokens" className="flex items-center gap-2">
            <Coins className="w-4 h-4" />
            Token Contracts ({filteredTokens.length})
          </TabsTrigger>
          <TabsTrigger value="nfts" className="flex items-center gap-2">
            <Image className="w-4 h-4" />
            NFT Contracts ({filteredNFTs.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-4">
          {filteredTokens.length === 0 ? (
            <Card className="bg-card/50 backdrop-blur-sm border-border/50">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Coins className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Token Contracts</h3>
                <p className="text-muted-foreground text-center mb-4">
                  You haven't created any token contracts yet.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTokens.map((contract) => (
                <Card key={contract.contractAddress} className="bg-card/50 backdrop-blur-sm border-border/50">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{contract.name}</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {contract.symbol}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {contract.chain}
                          </Badge>
                        </CardDescription>
                      </div>
                      {contract.imageURI && (
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Coins className="w-6 h-6 text-primary" />
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Contract:</span>
                      <div className="flex items-center gap-1">
                        <code className="text-xs">{contract.contractAddress.slice(0, 10)}...</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(contract.contractAddress, "Contract address")}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(getExplorerUrl(contract.chain, contract.contractAddress), "_blank")}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Decimals:</span>
                      <span>{contract.decimals}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Deployed:</span>
                      <span>{new Date(contract.deployedAt).toLocaleDateString()}</span>
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          className="w-full"
                          variant="gradient"
                          onClick={() =>
                            setMintTokenData({
                              contractAddress: contract.contractAddress,
                              toAddress: "",
                              amount: "",
                              chain: contract.chain,
                            })
                          }
                        >
                          <Hammer className="w-4 h-4 mr-2" />
                          Mint Tokens
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Mint {contract.symbol} Tokens</DialogTitle>
                          <DialogDescription>
                            Create new tokens and send them to any address
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Recipient Address</Label>
                            <Input
                              placeholder="0x..."
                              value={mintTokenData.toAddress}
                              onChange={(e) =>
                                setMintTokenData({ ...mintTokenData, toAddress: e.target.value })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Amount</Label>
                            <Input
                              placeholder="100"
                              type="number"
                              value={mintTokenData.amount}
                              onChange={(e) =>
                                setMintTokenData({ ...mintTokenData, amount: e.target.value })
                              }
                            />
                          </div>
                          <Button
                            onClick={mintToken}
                            disabled={mintLoading === contract.contractAddress}
                            className="w-full"
                            variant="gradient"
                          >
                            {mintLoading === contract.contractAddress ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Minting...
                              </>
                            ) : (
                              <>
                                <Hammer className="w-4 h-4 mr-2" />
                                Mint Tokens
                              </>
                            )}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="nfts" className="space-y-4">
          {filteredNFTs.length === 0 ? (
            <Card className="bg-card/50 backdrop-blur-sm border-border/50">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Image className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No NFT Contracts</h3>
                <p className="text-muted-foreground text-center mb-4">
                  You haven't created any NFT contracts yet.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredNFTs.map((contract) => (
                <Card key={contract.contractAddress} className="bg-card/50 backdrop-blur-sm border-border/50">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{contract.name}</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {contract.symbol}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {contract.chain}
                          </Badge>
                        </CardDescription>
                      </div>
                      {contract.imageURI && (
                        <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                          <Image className="w-6 h-6 text-accent" />
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {contract.description && (
                      <p className="text-sm text-muted-foreground">{contract.description}</p>
                    )}
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Contract:</span>
                      <div className="flex items-center gap-1">
                        <code className="text-xs">{contract.contractAddress.slice(0, 10)}...</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(contract.contractAddress, "Contract address")}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(getExplorerUrl(contract.chain, contract.contractAddress), "_blank")}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Deployed:</span>
                      <span>{new Date(contract.deployedAt).toLocaleDateString()}</span>
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          className="w-full"
                          variant="gradient"
                          onClick={() =>
                            setMintNFTData({
                              contractAddress: contract.contractAddress,
                              toAddress: "",
                              name: "",
                              description: "",
                              image: "",
                              chain: contract.chain,
                            })
                          }
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Mint NFT
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Mint NFT from {contract.name}</DialogTitle>
                          <DialogDescription>
                            Create a new NFT and send it to any address
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Recipient Address</Label>
                            <Input
                              placeholder="0x..."
                              value={mintNFTData.toAddress}
                              onChange={(e) =>
                                setMintNFTData({ ...mintNFTData, toAddress: e.target.value })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>NFT Name</Label>
                            <Input
                              placeholder="My Ghost #1"
                              value={mintNFTData.name}
                              onChange={(e) =>
                                setMintNFTData({ ...mintNFTData, name: e.target.value })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Description</Label>
                            <Input
                              placeholder="A unique ghost spirit..."
                              value={mintNFTData.description}
                              onChange={(e) =>
                                setMintNFTData({ ...mintNFTData, description: e.target.value })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Image URL</Label>
                            <Input
                              placeholder="https://..."
                              value={mintNFTData.image}
                              onChange={(e) =>
                                setMintNFTData({ ...mintNFTData, image: e.target.value })
                              }
                            />
                          </div>
                          <Button
                            onClick={mintNFT}
                            disabled={mintLoading === contract.contractAddress}
                            className="w-full"
                            variant="gradient"
                          >
                            {mintLoading === contract.contractAddress ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Minting...
                              </>
                            ) : (
                              <>
                                <Plus className="w-4 h-4 mr-2" />
                                Mint NFT
                              </>
                            )}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContractsDashboard;