{"_format": "hh-sol-artifact-1", "contractName": "IERC1155Errors", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC1155InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC1155InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "idsLength", "type": "uint256"}, {"internalType": "uint256", "name": "valuesLength", "type": "uint256"}], "name": "ERC1155InvalidArrayLength", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "ERC1155InvalidOperator", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC1155InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC1155InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC1155MissingApprovalForAll", "type": "error"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}